<!DOCTYPE html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.language.nativeplatform</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async="async"></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
    <div class="root">
<nav class="navigation" id="navigation-wrapper">
    <div class="navigation--inner">
        <div class="navigation-title">
            <button class="menu-toggle" id="menu-toggle" type="button">toggle menu</button>
            <div class="library-name">
                    <a class="library-name--link" href="../../index.html">
                            gradle
                    </a>
            </div>
            <div class="library-version">
8.13            </div>
        </div>
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
    </div>
    <div class="navigation-controls">
        <button class="navigation-controls--btn navigation-controls--theme" id="theme-toggle-button" type="button">switch theme</button>
        <div class="navigation-controls--btn navigation-controls--search" id="searchBar" role="button">search in API</div>
    </div>
</nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="sidebar--inner" id="sideMenu"></div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="gradle::org.gradle.language.nativeplatform////PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><span class="current">org.gradle.language.nativeplatform</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="572051696%2FClasslikes%2F-1793262594" anchor-label="ComponentWithExecutable" id="572051696%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-executable/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span><span>Executable</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="572051696%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-executable/index.html">ComponentWithExecutable</a> : <a href="-component-with-native-runtime/index.html">ComponentWithNativeRuntime</a></div><div class="brief ">Represents a native component that produces an executable.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-976931136%2FClasslikes%2F-1793262594" anchor-label="ComponentWithInstallation" id="-976931136%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-installation/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span><span>Installation</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-976931136%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-installation/index.html">ComponentWithInstallation</a> : <a href="-component-with-native-runtime/index.html">ComponentWithNativeRuntime</a></div><div class="brief ">Represents a native component that produces an application installation.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1957745764%2FClasslikes%2F-1793262594" anchor-label="ComponentWithLinkFile" id="1957745764%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-link-file/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span>Link</span><wbr></wbr><span><span>File</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1957745764%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-link-file/index.html">ComponentWithLinkFile</a> : <a href="-component-with-native-runtime/index.html">ComponentWithNativeRuntime</a></div><div class="brief ">Represents a native component that produces a file to be used at link time.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1013631647%2FClasslikes%2F-1793262594" anchor-label="ComponentWithLinkUsage" id="-1013631647%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-link-usage/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span>Link</span><wbr></wbr><span><span>Usage</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1013631647%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-link-usage/index.html">ComponentWithLinkUsage</a> : <a href="-component-with-native-runtime/index.html">ComponentWithNativeRuntime</a></div><div class="brief ">Represents a native component whose link time file and dependencies are published for consumption by some other project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="140069159%2FClasslikes%2F-1793262594" anchor-label="ComponentWithNativeRuntime" id="140069159%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-native-runtime/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span>Native</span><wbr></wbr><span><span>Runtime</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="140069159%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-native-runtime/index.html">ComponentWithNativeRuntime</a> : <a href="../org.gradle.api.component/-software-component/index.html">SoftwareComponent</a></div><div class="brief ">Represents a component that produces outputs that run on a native platform.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2028353552%2FClasslikes%2F-1793262594" anchor-label="ComponentWithObjectFiles" id="2028353552%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-object-files/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span>Object</span><wbr></wbr><span><span>Files</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2028353552%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-object-files/index.html">ComponentWithObjectFiles</a> : <a href="-component-with-native-runtime/index.html">ComponentWithNativeRuntime</a></div><div class="brief ">Represents a component that produces object files.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2122290324%2FClasslikes%2F-1793262594" anchor-label="ComponentWithRuntimeFile" id="2122290324%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-runtime-file/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span>Runtime</span><wbr></wbr><span><span>File</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2122290324%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-runtime-file/index.html">ComponentWithRuntimeFile</a> : <a href="-component-with-native-runtime/index.html">ComponentWithNativeRuntime</a></div><div class="brief ">Represents a native component that produces a file to be used at runtime.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-207717583%2FClasslikes%2F-1793262594" anchor-label="ComponentWithRuntimeUsage" id="-207717583%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-runtime-usage/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span>Runtime</span><wbr></wbr><span><span>Usage</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-207717583%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-runtime-usage/index.html">ComponentWithRuntimeUsage</a> : <a href="-component-with-native-runtime/index.html">ComponentWithNativeRuntime</a></div><div class="brief ">Represents a native component whose runtime file and dependencies are published for consumption by some other project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="516673010%2FClasslikes%2F-1793262594" anchor-label="ComponentWithSharedLibrary" id="516673010%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-shared-library/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span>Shared</span><wbr></wbr><span><span>Library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="516673010%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-shared-library/index.html">ComponentWithSharedLibrary</a> : <a href="-component-with-link-file/index.html">ComponentWithLinkFile</a>, <a href="-component-with-runtime-file/index.html">ComponentWithRuntimeFile</a></div><div class="brief ">Represents a native component that produces a shared library.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1325566971%2FClasslikes%2F-1793262594" anchor-label="ComponentWithStaticLibrary" id="1325566971%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-with-static-library/index.html"><span>Component</span><wbr></wbr><span>With</span><wbr></wbr><span>Static</span><wbr></wbr><span><span>Library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1325566971%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-with-static-library/index.html">ComponentWithStaticLibrary</a> : <a href="-component-with-link-file/index.html">ComponentWithLinkFile</a></div><div class="brief ">Represents a component that produces a static library.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1679368323%2FClasslikes%2F-1793262594" anchor-label="DependentSourceSet" id="-1679368323%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependent-source-set/index.html"><span>Dependent</span><wbr></wbr><span>Source</span><wbr></wbr><span><span>Set</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1679368323%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-dependent-source-set/index.html">DependentSourceSet</a> : <a href="../org.gradle.language.base/-language-source-set/index.html">LanguageSourceSet</a></div><div class="brief ">A source set that depends on one or more <a href="../org.gradle.nativeplatform/-native-dependency-set/index.html">org.gradle.nativeplatform.NativeDependencySet</a>s to be built.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1722883937%2FClasslikes%2F-1793262594" anchor-label="HeaderExportingSourceSet" id="-1722883937%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-header-exporting-source-set/index.html"><span>Header</span><wbr></wbr><span>Exporting</span><wbr></wbr><span>Source</span><wbr></wbr><span><span>Set</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1722883937%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-header-exporting-source-set/index.html">HeaderExportingSourceSet</a> : <a href="../org.gradle.language.base/-language-source-set/index.html">LanguageSourceSet</a></div><div class="brief ">A source set that exposes headers</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1394043104%2FClasslikes%2F-1793262594" anchor-label="NativeResourceSet" id="-1394043104%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-resource-set/index.html"><span>Native</span><wbr></wbr><span>Resource</span><wbr></wbr><span><span>Set</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1394043104%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-resource-set/index.html">NativeResourceSet</a> : <a href="../org.gradle.language.base/-language-source-set/index.html">LanguageSourceSet</a></div><div class="brief ">A source set that provides resources.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
            </div>
        </div>
    </div>
</body>
</html>
