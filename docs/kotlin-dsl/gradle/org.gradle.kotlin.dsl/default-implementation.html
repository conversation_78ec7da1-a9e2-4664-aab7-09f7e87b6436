<!DOCTYPE html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>defaultImplementation</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async="async"></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
    <div class="root">
<nav class="navigation" id="navigation-wrapper">
    <div class="navigation--inner">
        <div class="navigation-title">
            <button class="menu-toggle" id="menu-toggle" type="button">toggle menu</button>
            <div class="library-name">
                    <a class="library-name--link" href="../../index.html">
                            gradle
                    </a>
            </div>
            <div class="library-version">
8.13            </div>
        </div>
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
    </div>
    <div class="navigation-controls">
        <button class="navigation-controls--btn navigation-controls--theme" id="theme-toggle-button" type="button">switch theme</button>
        <div class="navigation-controls--btn navigation-controls--search" id="searchBar" role="button">search in API</div>
    </div>
</nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="sidebar--inner" id="sideMenu"></div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="gradle::org.gradle.kotlin.dsl//defaultImplementation/org.gradle.platform.base.TypeBuilder[TypeParam(bounds=[kotlin.Any])]#kotlin.reflect.KClass[*]/PointingToDeclaration//-1867656071">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><a href="index.html">org.gradle.kotlin.dsl</a><span class="delimiter">/</span><span class="current">defaultImplementation</span></div>
  <div class="cover ">
    <h1 class="cover"><span>default</span><wbr></wbr><span><span>Implementation</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><span data-unresolved-link="org.gradle.api/Incubating///PointingToDeclaration/"><span class="token annotation builtin">Incubating</span></span></div></div><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><a href="default-implementation.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../org.gradle.platform.base/-type-builder/index.html#-1702102608%2FMain%2F-1867656071">TypeBuilder</a><span class="token operator">&lt;</span><a href="default-implementation.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="default-implementation.html"><span class="token function">defaultImplementation</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">implementation<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../org.gradle.platform.base/-type-builder/index.html#-1702102608%2FMain%2F-1867656071">TypeBuilder</a><span class="token operator">&lt;</span><a href="default-implementation.html">T</a><span class="token operator">&gt;</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/073314332697ba45c16c0a0ce1891fa6794179ff/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_cgmwxekq82whdgdahlr0cj8ii.kt#L41">source</a>)</span></span></div><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><span data-unresolved-link="org.gradle.api/Incubating///PointingToDeclaration/"><span class="token annotation builtin">Incubating</span></span></div></div><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><a href="default-implementation.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../org.gradle.platform.base/-type-builder/index.html#-1702102608%2FMain%2F-1867656071">TypeBuilder</a><span class="token operator">&lt;</span><a href="default-implementation.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="default-implementation.html"><span class="token function">defaultImplementation</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">implementation<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../org.gradle.platform.base/-type-builder/index.html#-1702102608%2FMain%2F-1867656071">TypeBuilder</a><span class="token operator">&lt;</span><a href="default-implementation.html">T</a><span class="token operator">&gt;</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/073314332697ba45c16c0a0ce1891fa6794179ff/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_cgmwxekq82whdgdahlr0cj8ii.kt#L41">source</a>)</span></span></div><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin-stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.platform.base/TypeBuilder/defaultImplementation/#java.lang.Class[*]/PointingToDeclaration/">org.gradle.platform.base.TypeBuilder.defaultImplementation</span>.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.platform.base/TypeBuilder/defaultImplementation/#java.lang.Class[*]/PointingToDeclaration/"><span>Type</span><wbr></wbr><span>Builder.</span><wbr></wbr><span>default</span><wbr></wbr><span><span>Implementation</span></span></span></div></span></div><div></div></div></div></div></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
            </div>
        </div>
    </div>
</body>
</html>
