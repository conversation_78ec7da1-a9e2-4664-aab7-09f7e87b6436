<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>AndSpec (Gradle API 8.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="description" content="declaration: package: org.gradle.api.specs, class: AndSpec">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.specs</a></div>
<h1 title="Class AndSpec" class="title">Class AndSpec&lt;T&gt;</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="CompositeSpec.html" title="class in org.gradle.api.specs">org.gradle.api.specs.CompositeSpec</a>&lt;T&gt;
<div class="inheritance">org.gradle.api.specs.AndSpec&lt;T&gt;</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - The target type for this Spec</dd>
</dl>
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">AndSpec&lt;T&gt;</span>
<span class="extends-implements">extends <a href="CompositeSpec.html" title="class in org.gradle.api.specs">CompositeSpec</a>&lt;T&gt;</span></div>
<div class="block">A <a href="CompositeSpec.html" title="class in org.gradle.api.specs"><code>CompositeSpec</code></a> which requires all its specs to be true in order to evaluate to true.
 Uses lazy evaluation.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;?&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#EMPTY" class="member-name-link">EMPTY</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">AndSpec</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.Iterable)" class="member-name-link">AndSpec</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;? extends <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&gt;&nbsp;specs)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.gradle.api.specs.Spec...)" class="member-name-link">AndSpec</a><wbr>(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;...&nbsp;specs)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#and(groovy.lang.Closure)" class="member-name-link">and</a><wbr>(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.22/html/gapi/groovy/lang/Closure.html" title="class or interface in groovy.lang" class="external-link">Closure</a>&nbsp;spec)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#and(org.gradle.api.specs.Spec)" class="member-name-link">and</a><wbr>(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&nbsp;spec)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Typed and() method for a single <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#and(org.gradle.api.specs.Spec...)" class="member-name-link">and</a><wbr>(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;...&nbsp;specs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#empty()" class="member-name-link">empty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findUnsatisfiedSpec(T)" class="member-name-link">findUnsatisfiedSpec</a><wbr>(<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&nbsp;object)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the first <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a> that is not satisfied by the object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSatisfiedBy(T)" class="member-name-link">isSatisfiedBy</a><wbr>(<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&nbsp;object)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.gradle.api.specs.CompositeSpec">Methods inherited from class&nbsp;org.gradle.api.specs.<a href="CompositeSpec.html" title="class in org.gradle.api.specs">CompositeSpec</a></h3>
<code><a href="CompositeSpec.html#equals(java.lang.Object)">equals</a>, <a href="CompositeSpec.html#getSpecs()">getSpecs</a>, <a href="CompositeSpec.html#hashCode()">hashCode</a>, <a href="CompositeSpec.html#isEmpty()">isEmpty</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone--" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize--" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass--" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify--" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll--" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString--" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait--" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait-long-" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait-long-int-" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="EMPTY">
<h3>EMPTY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;?&gt;</span>&nbsp;<span class="element-name">EMPTY</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>AndSpec</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AndSpec</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.gradle.api.specs.Spec...)">
<h3>AndSpec</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/SafeVarargs.html" title="class or interface in java.lang" class="external-link">@SafeVarargs</a>
</span><span class="modifiers">public</span>&nbsp;<span class="element-name">AndSpec</span><wbr><span class="parameters">(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;...&nbsp;specs)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.Iterable)">
<h3>AndSpec</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AndSpec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;? extends <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&gt;&nbsp;specs)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="isSatisfiedBy(T)">
<h3 id="isSatisfiedBy(java.lang.Object)">isSatisfiedBy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSatisfiedBy</span><wbr><span class="parameters">(<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&nbsp;object)</span></div>
</section>
</li>
<li>
<section class="detail" id="findUnsatisfiedSpec(T)">
<h3 id="findUnsatisfiedSpec(java.lang.Object)">findUnsatisfiedSpec</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/javax/annotation/Nullable.html" title="class or interface in javax.annotation" class="external-link">@Nullable</a>
<a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</span>&nbsp;<span class="element-name">findUnsatisfiedSpec</span><wbr><span class="parameters">(<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&nbsp;object)</span></div>
<div class="block">Finds the first <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a> that is not satisfied by the object.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>object</code> - to check specs against</dd>
<dt>Returns:</dt>
<dd>an unsatisfied spec or null</dd>
<dt>Since:</dt>
<dd>7.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="and(org.gradle.api.specs.Spec...)">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</span>&nbsp;<span class="element-name">and</span><wbr><span class="parameters">(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;...&nbsp;specs)</span></div>
</section>
</li>
<li>
<section class="detail" id="and(org.gradle.api.specs.Spec)">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</span>&nbsp;<span class="element-name">and</span><wbr><span class="parameters">(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&nbsp;spec)</span></div>
<div class="block">Typed and() method for a single <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>4.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="and(groovy.lang.Closure)">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</span>&nbsp;<span class="element-name">and</span><wbr><span class="parameters">(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.22/html/gapi/groovy/lang/Closure.html" title="class or interface in groovy.lang" class="external-link">Closure</a>&nbsp;spec)</span></div>
</section>
</li>
<li>
<section class="detail" id="empty()">
<h3>empty</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;T&gt;</span>&nbsp;<span class="element-name">empty</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
