<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>InputArtifactDependencies (Gradle API 8.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="description" content="declaration: package: org.gradle.api.artifacts.transform, annotation type: InputArtifactDependencies">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Optional&nbsp;|&nbsp;</li>
<li>Required</li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Element</li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.transform</a></div>
<h1 title="Annotation Type InputArtifactDependencies" class="title">Annotation Type InputArtifactDependencies</h1>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Retention.html" title="class or interface in java.lang.annotation" class="external-link">@Retention</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/RetentionPolicy.html#RUNTIME" title="class or interface in java.lang.annotation" class="external-link">RUNTIME</a>)
<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Target.html" title="class or interface in java.lang.annotation" class="external-link">@Target</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#METHOD" title="class or interface in java.lang.annotation" class="external-link">METHOD</a>)
<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Documented.html" title="class or interface in java.lang.annotation" class="external-link">@Documented</a>
<a href="../../reflect/InjectionPointQualifier.html" title="annotation in org.gradle.api.reflect">@InjectionPointQualifier</a>(<a href="../../reflect/InjectionPointQualifier.html#supportedTypes()">supportedTypes</a>=<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection.class</a>)
</span><span class="modifiers">public @interface </span><span class="element-name type-name-label">InputArtifactDependencies</span></div>
<div class="block">Attach this annotation to an abstract getter that should receive the <em>artifact dependencies</em> of the <a href="InputArtifact.html" title="annotation in org.gradle.api.artifacts.transform"><code>InputArtifact</code></a> of an artifact transform.

 <p>
     For example, when a project depends on <code>spring-web</code>, when the project is transformed (i.e. the project is the input artifact),
     the input artifact dependencies are the file collection containing the <code>spring-web</code> JAR and all its dependencies like e.g. the <code>spring-core</code> JAR.

     The abstract getter must be declared as type <a href="../../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a>.
     The order of the files matches that of the dependencies declared for the input artifact.
 </p>

 <p>Example usage:</p>
 <pre class='autoTested'>
 import org.gradle.api.artifacts.transform.TransformParameters;

 public abstract class MyTransform implements TransformAction&lt;TransformParameters.None&gt; {

     @InputArtifact
     public abstract Provider&lt;FileSystemLocation&gt; getInputArtifact();

     @InputArtifactDependencies
     public abstract FileCollection getDependencies();

     @Override
     public void transform(TransformOutputs outputs) {
         FileCollection dependencies = getDependencies();
         // Do something with the dependencies
     }
 }
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>5.3</dd>
</dl>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
