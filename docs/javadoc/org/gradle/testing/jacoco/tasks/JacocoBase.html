<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>JacocoBase (Gradle API 8.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="description" content="declaration: package: org.gradle.testing.jacoco.tasks, class: JacocoBase">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.gradle.testing.jacoco.tasks</a></div>
<h1 title="Class JacocoBase" class="title">Class JacocoBase</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.gradle.api.internal.AbstractTask
<div class="inheritance"><a href="../../../api/DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a>
<div class="inheritance">org.gradle.testing.jacoco.tasks.JacocoBase</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../../../api/Named.html" title="interface in org.gradle.api">Named</a></code>, <code><a href="../../../api/plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="JacocoReportBase.html" title="class in org.gradle.testing.jacoco.tasks">JacocoReportBase</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations"><a href="../../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../../work/DisableCachingByDefault.html#because()">because</a>="Abstract super-class, not to be instantiated directly")
</span><span class="modifiers">public abstract class </span><span class="element-name type-name-label">JacocoBase</span>
<span class="extends-implements">extends <a href="../../../api/DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></span></div>
<div class="block">Base class for Jacoco tasks.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.gradle.api.Task">Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a></h2>
<code><a href="../../../api/Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.gradle.api.Task">Fields inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../../api/Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../../api/Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../../api/Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../../api/Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../../api/Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../../api/Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../../api/Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../../api/Task.html#TASK_TYPE">TASK_TYPE</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">JacocoBase</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../api/file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJacocoClasspath()" class="member-name-link">getJacocoClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Classpath containing Jacoco classes for use by the task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJacocoClasspath(org.gradle.api.file.FileCollection)" class="member-name-link">setJacocoClasspath</a><wbr>(<a href="../../../api/file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;jacocoClasspath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.gradle.api.DefaultTask">Methods inherited from class&nbsp;org.gradle.api.<a href="../../../api/DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../../api/DefaultTask.html#compareTo(org.gradle.api.Task)">compareTo</a>, <a href="../../../api/DefaultTask.html#configure(groovy.lang.Closure)">configure</a>, <a href="../../../api/DefaultTask.html#dependsOn(java.lang.Object...)">dependsOn</a>, <a href="../../../api/DefaultTask.html#doFirst(groovy.lang.Closure)">doFirst</a>, <a href="../../../api/DefaultTask.html#doFirst(java.lang.String,org.gradle.api.Action)">doFirst</a>, <a href="../../../api/DefaultTask.html#doFirst(org.gradle.api.Action)">doFirst</a>, <a href="../../../api/DefaultTask.html#doLast(groovy.lang.Closure)">doLast</a>, <a href="../../../api/DefaultTask.html#doLast(java.lang.String,org.gradle.api.Action)">doLast</a>, <a href="../../../api/DefaultTask.html#doLast(org.gradle.api.Action)">doLast</a>, <a href="../../../api/DefaultTask.html#finalizedBy(java.lang.Object...)">finalizedBy</a>, <a href="../../../api/DefaultTask.html#getActions()">getActions</a>, <a href="../../../api/DefaultTask.html#getAnt()">getAnt</a>, <a href="../../../api/DefaultTask.html#getDependsOn()">getDependsOn</a>, <a href="../../../api/DefaultTask.html#getDescription()">getDescription</a>, <a href="../../../api/DefaultTask.html#getDestroyables()">getDestroyables</a>, <a href="../../../api/DefaultTask.html#getDidWork()">getDidWork</a>, <a href="../../../api/DefaultTask.html#getEnabled()">getEnabled</a>, <a href="../../../api/DefaultTask.html#getExtensions()">getExtensions</a>, <a href="../../../api/DefaultTask.html#getFinalizedBy()">getFinalizedBy</a>, <a href="../../../api/DefaultTask.html#getGroup()">getGroup</a>, <a href="../../../api/DefaultTask.html#getInputs()">getInputs</a>, <a href="../../../api/DefaultTask.html#getLocalState()">getLocalState</a>, <a href="../../../api/DefaultTask.html#getLogger()">getLogger</a>, <a href="../../../api/DefaultTask.html#getLogging()">getLogging</a>, <a href="../../../api/DefaultTask.html#getMustRunAfter()">getMustRunAfter</a>, <a href="../../../api/DefaultTask.html#getName()">getName</a>, <a href="../../../api/DefaultTask.html#getOutputs()">getOutputs</a>, <a href="../../../api/DefaultTask.html#getPath()">getPath</a>, <a href="../../../api/DefaultTask.html#getProject()">getProject</a>, <a href="../../../api/DefaultTask.html#getShouldRunAfter()">getShouldRunAfter</a>, <a href="../../../api/DefaultTask.html#getState()">getState</a>, <a href="../../../api/DefaultTask.html#getTaskDependencies()">getTaskDependencies</a>, <a href="../../../api/DefaultTask.html#getTemporaryDir()">getTemporaryDir</a>, <a href="../../../api/DefaultTask.html#getTimeout()">getTimeout</a>, <a href="../../../api/DefaultTask.html#hasProperty(java.lang.String)">hasProperty</a>, <a href="../../../api/DefaultTask.html#mustRunAfter(java.lang.Object...)">mustRunAfter</a>, <a href="../../../api/DefaultTask.html#onlyIf(groovy.lang.Closure)">onlyIf</a>, <a href="../../../api/DefaultTask.html#onlyIf(java.lang.String,org.gradle.api.specs.Spec)">onlyIf</a>, <a href="../../../api/DefaultTask.html#onlyIf(org.gradle.api.specs.Spec)">onlyIf</a>, <a href="../../../api/DefaultTask.html#property(java.lang.String)">property</a>, <a href="../../../api/DefaultTask.html#setActions(java.util.List)">setActions</a>, <a href="../../../api/DefaultTask.html#setDependsOn(java.lang.Iterable)">setDependsOn</a>, <a href="../../../api/DefaultTask.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../api/DefaultTask.html#setDidWork(boolean)">setDidWork</a>, <a href="../../../api/DefaultTask.html#setEnabled(boolean)">setEnabled</a>, <a href="../../../api/DefaultTask.html#setFinalizedBy(java.lang.Iterable)">setFinalizedBy</a>, <a href="../../../api/DefaultTask.html#setGroup(java.lang.String)">setGroup</a>, <a href="../../../api/DefaultTask.html#setMustRunAfter(java.lang.Iterable)">setMustRunAfter</a>, <a href="../../../api/DefaultTask.html#setOnlyIf(groovy.lang.Closure)">setOnlyIf</a>, <a href="../../../api/DefaultTask.html#setOnlyIf(java.lang.String,org.gradle.api.specs.Spec)">setOnlyIf</a>, <a href="../../../api/DefaultTask.html#setOnlyIf(org.gradle.api.specs.Spec)">setOnlyIf</a>, <a href="../../../api/DefaultTask.html#setProperty(java.lang.String,java.lang.Object)">setProperty</a>, <a href="../../../api/DefaultTask.html#setShouldRunAfter(java.lang.Iterable)">setShouldRunAfter</a>, <a href="../../../api/DefaultTask.html#shouldRunAfter(java.lang.Object...)">shouldRunAfter</a>, <a href="../../../api/DefaultTask.html#usesService(org.gradle.api.provider.Provider)">usesService</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.gradle.api.internal.AbstractTask">Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone--" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals-java.lang.Object-" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize--" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass--" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode--" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify--" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll--" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString--" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait--" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait-long-" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait-long-int-" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.gradle.api.Task">Methods inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../../api/Task.html#doNotTrackState(java.lang.String)">doNotTrackState</a>, <a href="../../../api/Task.html#getConvention()">getConvention</a>, <a href="../../../api/Task.html#notCompatibleWithConfigurationCache(java.lang.String)">notCompatibleWithConfigurationCache</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>JacocoBase</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JacocoBase</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getJacocoClasspath()">
<h3>getJacocoClasspath</h3>
<div class="member-signature"><span class="annotations"><a href="../../../api/tasks/Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../api/file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></span>&nbsp;<span class="element-name">getJacocoClasspath</span>()</div>
<div class="block">Classpath containing Jacoco classes for use by the task.</div>
</section>
</li>
<li>
<section class="detail" id="setJacocoClasspath(org.gradle.api.file.FileCollection)">
<h3>setJacocoClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJacocoClasspath</span><wbr><span class="parameters">(<a href="../../../api/file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;jacocoClasspath)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
