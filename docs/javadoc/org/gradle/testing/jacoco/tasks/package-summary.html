<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>org.gradle.testing.jacoco.tasks (Gradle API 8.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="description" content="declaration: package: org.gradle.testing.jacoco.tasks">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li><a href="#package-description">Description</a>&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.gradle.testing.jacoco.tasks" class="title">Package org.gradle.testing.jacoco.tasks</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.gradle.testing.jacoco.tasks</span></div>
<section class="package-description" id="package-description">
<div class="block">Tasks to work with the JaCoCo code coverage library.</div>
</section>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="rules/package-summary.html">org.gradle.testing.jacoco.tasks.rules</a></div>
<div class="col-last even-row-color">
<div class="block">Implementations for Jacoco code coverage rules.</div>
</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel" aria-labelledby="class-summary-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JacocoBase.html" title="class in org.gradle.testing.jacoco.tasks">JacocoBase</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for Jacoco tasks.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JacocoCoverageVerification.html" title="class in org.gradle.testing.jacoco.tasks">JacocoCoverageVerification</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Task for verifying code coverage metrics.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JacocoReport.html" title="class in org.gradle.testing.jacoco.tasks">JacocoReport</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Task to generate HTML, Xml and CSV reports of Jacoco coverage data.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JacocoReportBase.html" title="class in org.gradle.testing.jacoco.tasks">JacocoReportBase</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Base class for Jacoco report tasks.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="JacocoReportsContainer.html" title="interface in org.gradle.testing.jacoco.tasks">JacocoReportsContainer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">The reporting configuration for the <a href="JacocoReport.html" title="class in org.gradle.testing.jacoco.tasks"><code>JacocoReport</code></a> task.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
