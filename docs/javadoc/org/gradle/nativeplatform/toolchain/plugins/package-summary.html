<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>org.gradle.nativeplatform.toolchain.plugins (Gradle API 8.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="description" content="declaration: package: org.gradle.nativeplatform.toolchain.plugins">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li><a href="#package-description">Description</a>&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.gradle.nativeplatform.toolchain.plugins" class="title">Package org.gradle.nativeplatform.toolchain.plugins</h1>
</div>
<hr>
<div class="package-signature"><span class="annotations"><a href="../../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
</span>package <span class="element-name">org.gradle.nativeplatform.toolchain.plugins</span></div>
<section class="package-description" id="package-description">
<div class="block">Built-in tool chain support.</div>
</section>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.gradle.nativeplatform.toolchain</a></div>
<div class="col-last even-row-color">
<div class="block">Classes that allow C++ tool chains to be configured.</div>
</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ClangCompilerPlugin.html" title="class in org.gradle.nativeplatform.toolchain.plugins">ClangCompilerPlugin</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A <a href="../../../api/Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which makes the <a href="http://clang.llvm.org">Clang</a> compiler available for compiling C/C++ code.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GccCompilerPlugin.html" title="class in org.gradle.nativeplatform.toolchain.plugins">GccCompilerPlugin</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A <a href="../../../api/Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which makes the <a href="http://gcc.gnu.org/">GNU GCC/G++ compiler</a> available for compiling C/C++ code.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MicrosoftVisualCppCompilerPlugin.html" title="class in org.gradle.nativeplatform.toolchain.plugins">MicrosoftVisualCppCompilerPlugin</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A <a href="../../../api/Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which makes the Microsoft Visual C++ compiler available to compile C/C++ code.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SwiftCompilerPlugin.html" title="class in org.gradle.nativeplatform.toolchain.plugins">SwiftCompilerPlugin</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A <a href="../../../api/Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which makes the <a href="https://swift.org/compiler-stdlib/">Swiftc</a> compiler available for compiling Swift code.</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
