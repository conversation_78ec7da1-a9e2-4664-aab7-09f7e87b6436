package net.fabricmc.revive;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.player.PlayerInventory;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.screen.ScreenHandler;
import net.minecraft.screen.ScreenHandlerType;
import net.minecraft.screen.slot.Slot;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import com.mojang.authlib.GameProfile;
import net.minecraft.nbt.NbtCompound;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public  class ReviveScreenHandler extends ScreenHandler {
    private final PlayerEntity player;
    private static final int SIZE = 54;
    private static final String BANNED_PLAYERS_FILE = "banned_players.json";
    private static final Gson GSON = new Gson();
    private List<BannedPlayer> bannedPlayers;

    public ReviveScreenHandler(int syncId, PlayerInventory playerInventory) {
        super(ScreenHandlerType.GENERIC_9X6, syncId);
        this.player = playerInventory.player;
        this.bannedPlayers = loadBannedPlayers();

        for (int i = 0; i < Math.min(bannedPlayers.size(), SIZE); i++) {
            BannedPlayer bannedPlayer = bannedPlayers.get(i);
            ItemStack skull = new ItemStack(Items.PLAYER_HEAD);

            // TODO: Set skull owner using NBT (API changed in 1.21.5)
            // For now, just use a regular player head

            this.addSlot(new SkullSlot(skull, i, bannedPlayer));
        }
    }

    @Override
    public boolean canUse(PlayerEntity player) {
        return true;
    }

    @Override
    public ItemStack quickMove(PlayerEntity player, int slot) {
        // Since this is a special UI for displaying banned players,
        // we don't want to allow quick-moving items
        return ItemStack.EMPTY;
    }

    private class SkullSlot extends Slot {
        private final BannedPlayer bannedPlayer;

        public SkullSlot(ItemStack stack, int index, BannedPlayer bannedPlayer) {
            super(null, index, 0, 0);
            this.setStack(stack);
            this.bannedPlayer = bannedPlayer;
        }

        @Override
        public void onTakeItem(PlayerEntity player, ItemStack stack) {
            MinecraftServer server = player.getServer();
            if (server != null) {
                unbanPlayer(server, bannedPlayer);

                // Zvuk beaconu pri odbanovaní
                player.getWorld().playSound(null, player.getBlockPos(), SoundEvents.BLOCK_BEACON_ACTIVATE, SoundCategory.BLOCKS, 1.0f, 1.0f);

                // Odošle správu do chatu všetkým hráčom
                server.getPlayerManager().broadcast(Text.literal(bannedPlayer.name + " dostal/a druhú šancu!").formatted(Formatting.GREEN), false);

                // Odstráni jeden Revive Beacon z ruky hráča
                player.getMainHandStack().decrement(1);
            }
        }
    }

    private List<BannedPlayer> loadBannedPlayers() {
        File file = new File(BANNED_PLAYERS_FILE);
        if (!file.exists()) return new ArrayList<>();

        try (FileReader reader = new FileReader(file)) {
            Type listType = new TypeToken<List<BannedPlayer>>() {}.getType();
            return GSON.fromJson(reader, listType);
        } catch (IOException e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    private void saveBannedPlayers(List<BannedPlayer> players) {
        try (FileWriter writer = new FileWriter(BANNED_PLAYERS_FILE)) {
            GSON.toJson(players, writer);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void unbanPlayer(MinecraftServer server, BannedPlayer bannedPlayer) {
        List<BannedPlayer> players = loadBannedPlayers();
        players.removeIf(p -> p.uuid.equals(bannedPlayer.uuid));
        saveBannedPlayers(players);
    }

    private static class BannedPlayer {
        String name;
        UUID uuid;
    }
}