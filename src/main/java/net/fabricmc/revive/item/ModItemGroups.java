package net.fabricmc.revive.item;

import net.fabricmc.revive.Revive;
import net.minecraft.item.ItemGroup;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

public class ModItemGroups {
    public static final ItemGroup REVIVE_ITEMS_GROUP = Registry.register(Registries.ITEM_GROUP,
            Identifier.of(Revive.MOD_ID, "revive_items"),
            ItemGroup.create(ItemGroup.Row.TOP, 0)
                    .icon(() -> new ItemStack(Items.NETHER_STAR))
                    .displayName(Text.translatable("itemgroup.revive.items"))
                    .entries((displayContext, entries) -> {
                        entries.add(Revive.REVIVE_BEACON);
                        entries.add(Revive.HEART_ITEM);
                    })
                    .build());

    public static void registerItemGroups() {
        System.out.println("Registering Item Groups for " + Revive.MOD_ID);
    }
}
