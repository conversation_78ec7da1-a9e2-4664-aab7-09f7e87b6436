package net.fabricmc.revive;

import net.fabricmc.api.ModInitializer;
import net.fabricmc.revive.items.HeartItem;
import net.fabricmc.revive.items.ReviveBeaconItem;
import net.minecraft.item.Item;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.util.Identifier;

public class Revive implements ModInitializer {
    public static final String MOD_ID = "revive";

    // Items will be registered in onInitialize
    public static Item REVIVE_BEACON;
    public static Item HEART_ITEM;

    @Override
    public void onInitialize() {
        // Register items
        REVIVE_BEACON = Registry.register(
            Registries.ITEM,
            Identifier.of(MOD_ID, "revive_beacon"),
            new ReviveBeaconItem(new Item.Settings())
        );

        HEART_ITEM = Registry.register(
            Registries.ITEM,
            Identifier.of(MOD_ID, "heart"),
            new HeartItem(new Item.Settings().maxCount(64))
        );

        System.out.println("Revive mod loaded successfully!");
    }
}
